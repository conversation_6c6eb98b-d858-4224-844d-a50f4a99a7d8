<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory\Catalog\Messenger;

use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\ChannelCollection;
use App\Catalog\Message\ChannelPricing;
use App\Catalog\Message\ChannelPricingCollection;
use App\Catalog\Message\LeafletCollection;
use App\Catalog\Message\Price;
use App\Catalog\Message\ProductVariant\Amount;
use App\Catalog\Message\ProductVariant\ProductOptions;
use App\Catalog\Message\ProductVariant\Translation;
use App\Catalog\Message\ProductVariant\UpsertProductVariant;
use App\Catalog\Message\TranslationCollection;
use App\Catalog\Message\TranslationInterface;
use App\Catalog\Message\UpsertLeaflet;

final class UpsertProductVariantFactory
{
    /**
     * @param ProductOptions|false|null $productOptions when (omitted or) false it will set the default productOptions ({@see ProductOptionsFactory::create()})
     */
    public static function create(
        string $code = 'default_code',
        string $productCode = 'default_product_code',
        bool $enabled = true,
        TranslationCollection $translations = null,
        ChannelCollection $channels = null,
        ChannelPricingCollection $channelPricing = null,
        ProductOptions|false|null $productOptions = false,
        Price $costPrice = null,
        ?string $supplierVariantName = 'default_supplier_variant_name',
        ?string $supplierVariantCode = 'default_supplier_variant_code',
        bool $prescriptionRequired = false,
        int $maximumQuantityPerOrder = 1,
        ?string $supplierCode = null,
        bool $preferredSupplier = false,
        ?int $preferredVariantForMinimumDailyOrders = null,
        ?string $zIndex = null,
        int $quantityMultiplier = 1,
        bool $shippingRequired = true,
        ?int $repeatIntervalInDays = null,
    ): UpsertProductVariant {
        if ($productOptions === false) {
            $productOptions = ProductOptionsFactory::create();
        }

        return new UpsertProductVariant(
            code: $code,
            productCode: $productCode,
            enabled: $enabled,
            translations: $translations ?? new TranslationCollection(),
            channels: $channels ?? new ChannelCollection(),
            channelPricings: $channelPricing ?? new ChannelPricingCollection(),
            costPrice: $costPrice ?? PriceFactory::create(),
            supplierVariantName: $supplierVariantName,
            supplierVariantCode: $supplierVariantCode,
            prescriptionRequired: $prescriptionRequired,
            maximumQuantityPerOrder: $maximumQuantityPerOrder,
            supplierCode: $supplierCode,
            preferredSupplier: $preferredSupplier,
            quantityMultiplier: $quantityMultiplier,
            leaflets: new LeafletCollection(),
            productOptions: $productOptions,
            preferredVariantForMinimumDailyOrders: $preferredVariantForMinimumDailyOrders,
            zIndex: $zIndex,
            shippingRequired: $shippingRequired,
            repeatIntervalInDays: $repeatIntervalInDays,
        );
    }

    /**
     * @param array<string, mixed> $data
     */
    public static function createFromArray(array $data): UpsertProductVariant
    {
        return new UpsertProductVariant(
            code: $data['code'],
            productCode: $data['productCode'],
            enabled: $data['enabled'],
            translations: new TranslationCollection(
                ...array_map(
                    static fn (array $translation): TranslationInterface => new Translation(...$translation),
                    $data['translations'] ?? [],
                ),
            ),
            channels: new ChannelCollection(
                ...array_map(
                    static fn (string $channel): string => $channel,
                    $data['channels'] ?? [],
                ),
            ),
            channelPricings: new ChannelPricingCollection(
                ...array_map(
                    static fn (array $channelPricing): ChannelPricing => new ChannelPricing(
                        channelCode: $channelPricing['channelCode'],
                        enabled: $channelPricing['enabled'],
                        price: new Price(...$channelPricing['price']),
                    ),
                    $data['channelPricings'] ?? [],
                ),
            ),
            costPrice: new Price(...$data['costPrice']),
            supplierVariantName: $data['supplierVariantName'] ?? null,
            supplierVariantCode: $data['supplierVariantCode'] ?? null,
            prescriptionRequired: $data['prescriptionRequired'] ?? false,
            maximumQuantityPerOrder: $data['maximumQuantityPerOrder'] ?? 1,
            supplierCode: $data['supplierCode'] ?? null,
            preferredSupplier: $data['preferredSupplier'] ?? false,
            quantityMultiplier: $data['quantityMultiplier'] ?? 1,
            leaflets: new LeafletCollection(
                ...array_map(
                    static fn (array $leaflet): UpsertLeaflet => new UpsertLeaflet(
                        locale: $leaflet['locale'],
                        attachmentUrl: $leaflet['attachmentUrl'],
                        client: OriginatingClient::tryFrom($leaflet['client']) ?? OriginatingClient::default(),
                    ),
                    $data['leaflets'] ?? [],
                ),
            ),
            productOptions: isset($data['productOptions'])
                ? new ProductOptions(
                    form: $data['productOptions']['form'],
                    dosage: new Amount(...$data['productOptions']['dosage']),
                    packSize: new Amount(...$data['productOptions']['packSize']),
                )
                : null,
            preferredVariantForMinimumDailyOrders: $data['preferredVariantForMinimumDailyOrders'] ?? null,
            zIndex: $data['zIndex'] ?? null,
            note: $data['note'] ?? null,
            shippingRequired: $data['shippingRequired'] ?? true,
            repeatIntervalInDays: $data['repeatIntervalInDays'] ?? null,
        );
    }
}
