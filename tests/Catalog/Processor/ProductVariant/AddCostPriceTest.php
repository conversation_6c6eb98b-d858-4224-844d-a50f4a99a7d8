<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\CostPriceProcessor;
use App\Entity\Product\ProductVariantInterface;
use App\Tests\Util\Factory\Catalog\Messenger\PriceFactory;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

class AddCostPriceTest extends TestCase
{
    public function testExecuteWithBaseCurrency(): void
    {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $upsertProductVariant = UpsertProductVariantFactory::create(costPrice: PriceFactory::create(
            amount: 100,
            currency: 'EUR'
        ));
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert
        $productVariant->expects($this->once())->method('setCostPrice')->with(100);

        // Act
        $addCostPrice = new CostPriceProcessor($currencyConverter);
        $addCostPrice->execute($upsertProductVariant, $productVariant);
    }

    public function testExecuteWithNonBaseCurrency(): void
    {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $upsertProductVariant = UpsertProductVariantFactory::create(costPrice: PriceFactory::create(
            amount: 100,
        ));
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert
        $currencyConverter->expects($this->once())->method('convert')->with(100, 'USD', 'EUR')->willReturn(90);
        $productVariant->expects($this->once())->method('setCostPrice')->with(90);

        // Act
        $addCostPrice = new CostPriceProcessor($currencyConverter);
        $addCostPrice->execute($upsertProductVariant, $productVariant);
    }

    public function testExecuteWithGBPToEURConversion(): void
    {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $upsertProductVariant = UpsertProductVariantFactory::create(costPrice: PriceFactory::create(
            amount: 10000, // £100.00 in pence
            currency: 'GBP'
        ));
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - GBP to EUR conversion (using realistic exchange rate)
        // £100.00 should convert to approximately €113.64 (using 1.1364 rate)
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with(10000, 'GBP', 'EUR')
            ->willReturn(11364); // €113.64 in cents

        $productVariant->expects($this->once())->method('setCostPrice')->with(11364);

        // Act
        $addCostPrice = new CostPriceProcessor($currencyConverter);
        $addCostPrice->execute($upsertProductVariant, $productVariant);
    }
}
