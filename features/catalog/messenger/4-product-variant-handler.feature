Feature: Upsert the product variant from the catalog messenger queue
  In order to load a product variant into sylius
  As a queue worker
  I need to be able to execute message handler

  Background:
    Given there is a channel "dok_de"
    And there is a channel "dok_gb"
    And there is a supplier with identifier "apotheek-bad-nieuweschans"
    And there are no product variants enabled
    And there is a product named "Viagra" with code "7" of type "medication"

  Scenario: Cannot create a product variant without an existing product
    Given the product variant with code "7_5_prime_pharmacy_de" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "7_5_prime_pharmacy_de"
      productCode: "i-do-not-exists"
      enabled: true
      supplierVariantName: "Viagra 25mg 4 tabl."
      supplierVariantCode: N142231231
      prescriptionRequired: true
      maximumQuantityPerOrder: 8
      supplierCode: "prime-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 2265
        currency: EUR
      productOptions:
        form: tablet
        dosage:
          amount: "25"
          unit: mg
        packSize:
          amount: "4"
          unit: pieces
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      note: '<h1>Awesome note coming from Katana PIM!</h1>'
      """
    Then the product variant with code "7_5_prime_pharmacy_de" does not exist

  Scenario: Creating and updating a product variant for a product
    Given the product variant with code "7_5_prime_pharmacy_de" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "7_5_prime_pharmacy_de"
      productCode: "7"
      enabled: true
      supplierVariantName: "Viagra 25mg 4 tabl."
      supplierVariantCode: N142231231
      prescriptionRequired: true
      maximumQuantityPerOrder: 8
      supplierCode: "prime-pharmacy"
      preferredSupplier: false
      quantityMultiplier: 1
      shippingRequired: true
      costPrice:
        amount: 2265
        currency: EUR
      productOptions:
        form: tablet
        dosage:
          amount: "25"
          unit: mg
        packSize:
          amount: "4"
          unit: pieces
      leaflets:
        - locale: nl
          attachmentUrl: "https://dokteronline.com/leaflet-nl.pdf"
        - locale: en
          attachmentUrl: "https://dokteronline.com/leaflet-en.pdf"
      translations:
        - locale: nl
          name: "Viagra 25 mg 4 tabl."
          defaultUsageAdvice: "Lekker doen joh"
        - locale: en
          name: "Viagra 25 mg 4 tabl."
          defaultUsageAdvice: "Just do it"
      channels:
        - dok_nl
        - dok_gb
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 100
            currency: EUR
        - channelCode: dok_gb
          enabled: false
          price:
            amount: 150
            currency: GBP
      note: '<h1>Awesome note coming from Katana PIM!</h1>'
      """
    Then the product variant with code "7_5_prime_pharmacy_de" exists with product code "7"
    And the product variant has property "enabled" with value true
    And the product variant has property "supplierVariantName" with value "Viagra 25mg 4 tabl."
    And the product variant has property "supplierVariantCode" with value "N142231231"
    And the product variant has property "prescriptionRequired" with value true
    And the product variant has property "shippingRequired" with value true
    And the product variant has property "maximumQuantityPerOrder" with value 8
    And the product variant has property "preferredSupplier" with value false
    And the product variant has property "preferredVariantForMinimumDailyOrders" with value null
    And the product variant has property "zIndex" with value null
    And the product variant has property "repeatIntervalInDays" with value null
    And the product variant has property "note.message" with value "<h1>Awesome note coming from Katana PIM!</h1>"
    And the product variant has the following product option values:
    """yaml
    - code: tablet
      option.code: form
    - code: 7_dosage_25_mg
      option.code: 7_dosage
    - code: 7_packsize_4_pieces
      option.code: 7_packsize
    """
    And the product variant has the following channel pricing
    """yaml
    - channelCode: dok_nl
      enabled: true
      price: 100
    - channelCode: dok_gb
      enabled: false
      price: 200
    """
    And the product variant has the following translations
    """yaml
    - locale: nl
      name: "Viagra 25 mg 4 tabl."
      defaultUsageAdvice: "Lekker doen joh"
    - locale: en
      name: "Viagra 25 mg 4 tabl."
      defaultUsageAdvice: "Just do it"
    """
    And the product variant has the following leaflets
    """yaml
    - locale: nl
      attachmentUrl: "https://localhost/leaflets/d94485421ca4960d09fe48b28533785ab55b266fb67cc952cc101f5517ef14cf.pdf"
    - locale: en
      attachmentUrl: "https://localhost/leaflets/24dd2e50f4d83a4a212e5838893e9a3f5bd4226850b920ef16471ee6e3895921.pdf"
    """
    When I execute the upsert product variant handler with:
      """yaml
      code: "7_5_prime_pharmacy_de"
      productCode: "7"
      enabled: false
      supplierVariantName: "Viagra 25 mg 4 tabl."
      supplierVariantCode: N1441
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "prime-pharmacy"
      preferredSupplier: true
      preferredVariantForMinimumDailyOrders: 100
      zIndex: "1233sda1"
      quantityMultiplier: 1
      repeatIntervalInDays: 2000
      costPrice:
        amount: 1265
        currency: EUR
      translations:
        - locale: nl
          name: "Viagra 25mg 4 tabl."
          defaultUsageAdvice: "Lekker doen joh"
        - locale: en
          name: "Viagra 25mg 4 tabl."
          defaultUsageAdvice: null
      leaflets:
        - locale: en
          attachmentUrl: "https://dokteronline.com/leaflet-en.pdf"
      channels:
        - dok_nl
        - dok_gb
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 100
            currency: EUR
        - channelCode: dok_gb
          enabled: false
          price:
            amount: 150
            currency: GBP
      """
    And the product variant has property "enabled" with value false
    And the product variant has property "supplierVariantName" with value "Viagra 25 mg 4 tabl."
    And the product variant has property "supplierVariantCode" with value "N1441"
    And the product variant has property "prescriptionRequired" with value false
    And the product variant has property "maximumQuantityPerOrder" with value 10
    And the product variant has property "preferredSupplier" with value true
    And the product variant has property "preferredVariantForMinimumDailyOrders" with value 100
    And the product variant has property "zIndex" with value "1233sda1"
    And the product variant has property "note?.message" with value "null"
    And the product variant has property "repeatIntervalInDays" with value 2000
    And the product variant has the following product option values:
    """yaml
    """
    And the product variant has the following channel pricing
      """yaml
      - channelCode: dok_nl
        enabled: true
        price: 100
      - channelCode: dok_gb
        enabled: false
        price: 200
      """
    And the product variant has the following translations
      """yaml
      - locale: nl
        name: "Viagra 25mg 4 tabl."
        defaultUsageAdvice: "Lekker doen joh"
      - locale: en
        name: "Viagra 25mg 4 tabl."
        defaultUsageAdvice: null
      """
    And the product variant has the following leaflets
    """yaml
    - locale: en
      attachmentUrl: "https://localhost/leaflets/24dd2e50f4d83a4a212e5838893e9a3f5bd4226850b920ef16471ee6e3895921.pdf"
    """
    And no event is queued to send ProductBackInStock message for business unit code "dokteronline"

  Scenario: Creating and updating a product variant for a product in GBP
    Given there is an exchange rate from "EUR" to "GBP" with ratio 0.5903
    And the product variant with code "7_5_prime_pharmacy_de" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "7_5_prime_pharmacy_de"
      productCode: "7"
      enabled: true
      supplierVariantName: "Viagra 25mg 4 tabl."
      supplierVariantCode: N142231231
      prescriptionRequired: true
      maximumQuantityPerOrder: 8
      supplierCode: "prime-pharmacy"
      preferredSupplier: false
      quantityMultiplier: 1
      shippingRequired: true
      costPrice:
        amount: 2265
        currency: "GBP"
      productOptions:
        form: tablet
        dosage:
          amount: "25"
          unit: mg
        packSize:
          amount: "4"
          unit: pieces
      leaflets:
        - locale: nl
          attachmentUrl: "https://dokteronline.com/leaflet-nl.pdf"
        - locale: en
          attachmentUrl: "https://dokteronline.com/leaflet-en.pdf"
      translations:
        - locale: nl
          name: "Viagra 25 mg 4 tabl."
          defaultUsageAdvice: "Lekker doen joh"
        - locale: en
          name: "Viagra 25 mg 4 tabl."
          defaultUsageAdvice: "Just do it"
      channels:
        - dok_nl
        - dok_gb
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 100
            currency: EUR
        - channelCode: dok_gb
          enabled: false
          price:
            amount: 150
            currency: GBP
      note: '<h1>Awesome note coming from Katana PIM!</h1>'
      """
    Then the product variant with code "7_5_prime_pharmacy_de" exists with product code "7"
    And the product variant has property "costPrice" with value "3838"
