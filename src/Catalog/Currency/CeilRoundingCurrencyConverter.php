<?php

declare(strict_types=1);

namespace App\Catalog\Currency;

use App\Entity\Channel\Channel;
use App\Entity\Currency\Currency;
use InvalidArgumentException;
use RuntimeException;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

final class CeilRoundingCurrencyConverter implements CurrencyConverterInterface, ChannelCurrencyConverterInterface
{
    private const int CEIL_TO_HUNDREDS = 100;

    public function __construct(
        private readonly CurrencyConverterInterface $baseCurrencyConverter,
    ) {
    }

    public function convert(int $value, string $sourceCurrencyCode, string $targetCurrencyCode): int
    {
        if ($value === 0) {
            return 0;
        }

        if (!$this->shouldConvertAndCeilCurrency($targetCurrencyCode)) {
            return $value;
        }

        $convertedValue = $this->baseCurrencyConverter->convert($value, $sourceCurrencyCode, $targetCurrencyCode);

        return (int) ceil($convertedValue / self::CEIL_TO_HUNDREDS) * self::CEIL_TO_HUNDREDS;
    }

    public function convertForChannel(int $value, string $sourceCurrencyCode, Channel $targetChannel): int
    {
        $targetChannelCurrency = $targetChannel->getBaseCurrency();
        if (!$targetChannelCurrency instanceof Currency) {
            throw new InvalidArgumentException(sprintf('Provided channel "%s" does not have a base currency.', $targetChannel->getCode()));
        }

        $currencyCode = $targetChannelCurrency->getCode();
        if (!is_string($currencyCode) || $currencyCode === '') {
            throw new RuntimeException(sprintf("The base currency of provided channel '%s' does not have a currency code.", $targetChannel->getCode()));
        }

        return $this->convert($value, $sourceCurrencyCode, $currencyCode);
    }

    private function shouldConvertAndCeilCurrency(string $currencyCode): bool
    {
        return $currencyCode !== Currency::BASE_CURRENCY;
    }
}
